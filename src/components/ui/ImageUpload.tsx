import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/Button';
import clubIcon from '@/assets/images/club/club.png';
import { cn } from '@/lib/utils';

interface ImageUploadProps {
  value?: string | null;
  accept?: string;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  hasError?: boolean;
  errorMessage?: string;
  onChange: (file: File | null) => void;
  onRemove?: () => void;
  onInvalidFile?: (message: string) => void;
}

function isFileAccepted(file: File, accept?: string): boolean {
  if (!accept || accept === '*/*') return true;

  const acceptedList = accept
    .split(',')
    .map((s) => s.trim().toLowerCase())
    .filter(Boolean);

  const fileType = (file.type || '').toLowerCase();
  const fileName = (file.name || '').toLowerCase();

  // Match MIME types (e.g., image/png, image/jpeg, image/svg+xml)
  if (acceptedList.some((a) => !a.startsWith('.') && a === fileType)) return true;

  // Match wildcards like image/*
  if (
    fileType &&
    acceptedList.some((a) => a.endsWith('/*') && fileType.startsWith(a.replace('/*', '/')))
  )
    return true;

  // Match by extension patterns like .png, .jpg, .jpeg, .svg
  if (acceptedList.some((a) => a.startsWith('.') && fileName.endsWith(a))) return true;

  return false;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value,
  accept = 'image/*',
  className = '',
  disabled = false,
  placeholder = 'Upload Image',
  hasError = false,
  errorMessage,
  onChange,
  onRemove,
  onInvalidFile,
}) => {
  const [imagePreview, setImagePreview] = useState<string | null>(value || null);

  // Update imagePreview when value prop changes
  React.useEffect(() => {
    setImagePreview(value || null);
  }, [value]);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate against accept before reading/previewing
    if (!isFileAccepted(file, accept)) {
      if (onInvalidFile) onInvalidFile('Only PNG, JPEG, and SVG are allowed');
      // Reset any selected file
      if (fileInputRef.current) fileInputRef.current.value = '';
      onChange(null);
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      setImagePreview(result);
      onChange(file);
    };
    reader.readAsDataURL(file);
  };

  const handleRemoveImage = () => {
    setImagePreview(null);
    onChange(null);
    if (onRemove) {
      onRemove();
    }
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleUploadClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className={cn('flex flex-col', className)}>
      {/* Main content wrapper */}
      <div
        className={cn(
          'flex flex-col sm:flex-row items-start sm:items-end sm:space-x-4 gap-4 sm:gap-0'
        )}
      >
        <div className='flex flex-col gap-2 w-full'>
          {/* Image Preview */}
          {imagePreview ? (
            <div className='relative'>
              <img
                src={imagePreview}
                alt='Preview'
                className={cn(
                  'sm:w-[277px] sm:h-[138px] w-full h-auto aspect-4/3 object-cover rounded-lg border',
                  hasError && 'border-red-500'
                )}
              />
            </div>
          ) : (
            <div
              className={cn(
                'sm:w-[277px] sm:h-[138px] h-auto aspect-4/3 w-full rounded-lg flex items-center justify-center bg-muted cursor-pointer',
                hasError && 'border border-red-500'
              )}
              onClick={handleUploadClick}
            >
              <div className='text-center'>
                <img
                  src={clubIcon}
                  alt='Upload Image'
                  width={24}
                  height={24}
                  className='w-14 h-auto'
                />
              </div>
            </div>
          )}
          {/* Error message for mobile screens only */}
          {errorMessage && (
            <p className='text-[13px] text-destructive mt-1 sm:hidden'>{errorMessage}</p>
          )}
        </div>

        {/* Upload Controls */}
        <div className='flex flex-row gap-2'>
          <input
            ref={fileInputRef}
            type='file'
            accept={accept}
            onChange={handleImageChange}
            className='hidden'
            disabled={disabled}
          />

          {imagePreview && (
            <>
              <Button
                type='button'
                onClick={handleRemoveImage}
                variant='ghost'
                className='text-gray-500 text-sm hover:text-gray-600 border border-gray-300 rounded-md px-[16px] py-[12px] font-semibold text-[14px] leading-[20px]'
                size='sm'
                disabled={disabled}
              >
                Delete
              </Button>
            </>
          )}
          <Button
            type='button'
            onClick={handleUploadClick}
            className='flex text-primary bg-secondary hover:bg-[#E5E9FF] text-sm  px-[16px] py-[12px] font-semibold text-[14px] rounded-md leading-[20px]'
            size='sm'
            disabled={disabled}
          >
            {placeholder}
          </Button>

          {/* Error messaging is handled by the parent form */}
        </div>
      </div>

      {/* Error message for desktop screens only - positioned below everything */}
      {errorMessage && (
        <p className='text-[13px] text-destructive mt-2 hidden sm:block'>{errorMessage}</p>
      )}
    </div>
  );
};

export default ImageUpload;
